<template>
  <div class="fence-cursor-test-demo">
    <h3>围栏尺寸文本光标冲突修复测试</h3>
    
    <div class="test-controls">
      <el-button @click="createTestFence" type="primary">创建测试围栏</el-button>
      <el-button @click="createTestRuler" type="success">创建测试尺子</el-button>
      <el-button @click="clearAll" type="danger">清除所有</el-button>
    </div>
    
    <div class="test-info">
      <p><strong>测试说明：</strong></p>
      <ul>
        <li>点击"创建测试围栏"按钮创建一个带尺寸文本的圆形围栏</li>
        <li>点击"创建测试尺子"按钮创建一个测量尺子</li>
        <li>双击任意文本进行编辑，验证光标显示是否正确</li>
        <li>尝试同时编辑多个文本，验证冲突处理是否正确</li>
      </ul>
    </div>
    
    <div class="canvas-container" ref="canvasContainer">
      <canvas ref="testCanvas" width="800" height="600"></canvas>
    </div>
    
    <div class="test-status">
      <p>当前光标模式: <span class="status-text">{{ currentCursorMode }}</span></p>
      <p>当前编辑文本: <span class="status-text">{{ currentEditingText || '无' }}</span></p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import FabricCanvas from '@/utils/FabricCanvas'
import { EditableFence } from '@/utils/fence'
import { RulerManager } from '@/utils/ruler/ruler'

// 响应式数据
const canvasContainer = ref<HTMLDivElement>()
const testCanvas = ref<HTMLCanvasElement>()
const currentCursorMode = ref('default')
const currentEditingText = ref('')

// 画布和工具实例
let fabricCanvas: FabricCanvas | null = null
let rulerManager: RulerManager | null = null
let testFence: EditableFence | null = null

// 模拟的图片尺寸信息
const mockImgInfoSize = {
  scaleSize: 0.1, // 1像素 = 0.1米
  originX: 0,
  originY: 0,
  width: 800,
  height: 600
}

// 模拟的屏幕信息
const mockScreenInfo = {
  devicePixelRatio: 1,
  screenWidth: 800,
  screenHeight: 600,
  scaleX: 1,
  scaleY: 1
}

/**
 * 初始化测试画布
 */
const initTestCanvas = () => {
  if (!testCanvas.value) {
    ElMessage.error('画布元素未找到')
    return
  }

  try {
    // 创建FabricCanvas实例
    fabricCanvas = new FabricCanvas({
      containerId: 'test-canvas',
      width: 800,
      height: 600
    })

    // 创建尺子管理器
    rulerManager = new RulerManager(fabricCanvas.canvas)

    // 监听光标模式变化
    fabricCanvas.canvas.on('after:render', () => {
      updateCursorStatus()
    })

    ElMessage.success('测试画布初始化成功')
  } catch (error) {
    console.error('初始化测试画布失败:', error)
    ElMessage.error('初始化测试画布失败')
  }
}

/**
 * 创建测试围栏
 */
const createTestFence = () => {
  if (!fabricCanvas) {
    ElMessage.error('画布未初始化')
    return
  }

  try {
    // 清除现有围栏
    if (testFence) {
      testFence.destroy()
    }

    // 创建新的可编辑围栏
    testFence = new EditableFence(
      fabricCanvas,
      mockImgInfoSize,
      mockScreenInfo,
      'circle',
      'test-fence'
    )

    // 设置更新回调
    testFence.setUpdateCallback((fenceData) => {
      console.log('围栏数据更新:', fenceData)
      ElMessage.info(`围栏半径更新为: ${fenceData.points.radius.toFixed(2)}m`)
    })

    // 创建圆形围栏
    testFence.createCircleFence({ x: 200, y: 200 }, 80)

    ElMessage.success('测试围栏创建成功，双击文本可编辑')
  } catch (error) {
    console.error('创建测试围栏失败:', error)
    ElMessage.error('创建测试围栏失败')
  }
}

/**
 * 创建测试尺子
 */
const createTestRuler = () => {
  if (!rulerManager) {
    ElMessage.error('尺子管理器未初始化')
    return
  }

  try {
    // 创建测试尺子
    const ruler = rulerManager.createRuler({ x: 400, y: 300 })
    rulerManager.updateRuler(ruler, { x: 400, y: 300 }, { x: 500, y: 350 }, 50, false)

    ElMessage.success('测试尺子创建成功，双击文本可编辑')
  } catch (error) {
    console.error('创建测试尺子失败:', error)
    ElMessage.error('创建测试尺子失败')
  }
}

/**
 * 清除所有测试对象
 */
const clearAll = () => {
  if (testFence) {
    testFence.destroy()
    testFence = null
  }

  if (rulerManager) {
    rulerManager.removeAllRulers()
  }

  if (fabricCanvas) {
    fabricCanvas.canvas.clear()
  }

  currentEditingText.value = ''
  ElMessage.success('已清除所有测试对象')
}

/**
 * 更新光标状态显示
 */
const updateCursorStatus = () => {
  if (!fabricCanvas) return

  const canvas = fabricCanvas.canvas
  currentCursorMode.value = canvas.defaultCursor || 'default'

  // 检查是否有文本在编辑
  const activeObject = canvas.getActiveObject()
  if (activeObject && activeObject.type === 'i-text' && (activeObject as any).isEditing) {
    currentEditingText.value = `${activeObject.type} (${(activeObject as any).text})`
  } else {
    currentEditingText.value = ''
  }
}

onMounted(() => {
  // 设置canvas元素ID
  if (testCanvas.value) {
    testCanvas.value.id = 'test-canvas'
  }
  
  // 延迟初始化，确保DOM完全渲染
  setTimeout(() => {
    initTestCanvas()
  }, 100)
})

onUnmounted(() => {
  clearAll()
  if (fabricCanvas) {
    fabricCanvas.canvas.dispose()
  }
})
</script>

<style scoped>
.fence-cursor-test-demo {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-controls {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.test-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.test-info ul {
  margin: 10px 0 0 20px;
}

.test-info li {
  margin-bottom: 5px;
  line-height: 1.5;
}

.canvas-container {
  margin-bottom: 20px;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
}

.test-status {
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.test-status p {
  margin: 5px 0;
  font-size: 14px;
}

.status-text {
  font-weight: bold;
  color: #409EFF;
}
</style>
