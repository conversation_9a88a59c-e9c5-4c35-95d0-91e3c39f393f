<template>
  <div
    ref="canvasParent" 
    v-loading="state.imgLoading" 
    element-loading-text="地图加载中..."
    style="width: 100%; height: 100%"
  >
    <el-empty description="请选择楼层地图" v-if="state.empty" style="width: 100%; height: 100%" />
    <canvas :id="state.canvasId" style="width: 100%; height: 100%"></canvas>
  </div>
</template>

<script setup lang="ts">
import { AreaNode } from '@/api/master/area';
import { floorApi, floorVO } from '@/api/master/floor';
import { Iposition } from '@/api/master/floor/type';
import { canvasToImageWithDPR, type ScreenInfo } from '@/store/modules/canvas/drawing';
import FabricCanvas from '@/utils/FabricCanvas';
import {
  type CirclePoints,
  type RectanglePoints,
  type PolygonPoints,
  type AreaPosition,
  CanvasStateManager,
  EventHandlerUtils,
  DrawingStateManager,
  setAreaColor,
  setAreaHighlightAndDraggable,
  createFence,
  getAreaCenter,
  findAreaNodeById,
  updateAreaTreeNode,
  updateObjectPosition,
  ObjectPool,
  throttledRender,
  AreaDimensionManager,
  extendFabricCanvas
} from './2dMap';

import { RealtimePolygonDrawer, FenceFactory } from '@/utils/fence';

interface areaPostion {
  type: 'circle' | 'rectangle' | 'polygon' | 'none'
  points: CirclePoints | RectanglePoints | PolygonPoints
}

let map: FabricCanvas;
const canvasParent = ref<HTMLElement | null>(null);

// 主状态对象
const state = reactive({
  floorData: {} as floorVO,
  imgInfoSize: {} as Iposition,
  imgLoading: false,
  position: {} as any,
  empty: true,
  canvasId: computed(() => 'id-' + Math.random().toString(36).substring(2, 11)),
  lastRenderTime: 0,
  fps: 60,
  pendingRender: false,
  screenInfo: {
    screenWidth: 0,
    screenHeight: 0,
    devicePixelRatio: 1
  } as ScreenInfo
});

// 画布操作对象
const DrawingState = reactive({
  isMultiple: false,
  isMouseDown: false,
  drawType: '' as 'rectangle' | 'polygon' | 'circle' | 'none',
  areaObject: null as any,
  startPoint: { x: 0, y: 0 } as any,
  areaTree: [] as AreaNode[],
  areaData: {} as AreaNode,
  areaPostion: {} as areaPostion,
  // 尺寸标注相关状态
  dimensionMarkers: [] as any[], // 当前绘制区域的尺寸标注对象
  showDimensions: false, // 是否显示尺寸标注
  // 多边形绘制器
  polygonDrawer: null as RealtimePolygonDrawer | null
});

// 当前选中的区域ID
const currentSelectedAreaId = ref<number | null>(null);

// 缩放更新定时器
let zoomUpdateTimeout: number | null = null;

const getScreenInfo = (): ScreenInfo => ({
  screenWidth: state.screenInfo.screenWidth,
  screenHeight: state.screenInfo.screenHeight,
  devicePixelRatio: state.screenInfo.devicePixelRatio
});

/**
 * 获取楼层数据并初始化地图
 */
const getFloor = async (id: number) => {
  DrawingState.areaTree = [];
  
  if (!id) {
    state.floorData = {} as floorVO;
    state.imgInfoSize = {} as Iposition;
    state.empty = true;
    
    if (map?.canvas) {
      try {
        map.canvas.clear();
      } catch (error) {
        console.warn('清理画布失败:', error);
      }
    }
    return false;
  }

  try {
    state.imgLoading = true;
    const floorData = await floorApi.getfloor(id);
    state.floorData = floorData || {} as floorVO;
    
    if (floorData) {
      if (floorData.position) {
        try {
          state.imgInfoSize = JSON.parse(floorData.position);
        } catch (parseError) {
          state.imgInfoSize = {} as Iposition;
          ElMessage.warning('楼层位置数据格式错误');
        }
      } else {
        state.imgInfoSize = {} as Iposition;
      }
      
      if (floorData.mapUrl) {
        try {
          await initCanvas(floorData.mapUrl);
          return true;
        } catch (canvasError) {
          state.imgLoading = false;
          state.empty = true;
          ElMessage.error('加载地图失败，请刷新页面重试');
          return false;
        }
      } else {
        state.imgLoading = false;
        state.empty = true;
        ElMessage.warning('楼层地图URL不存在');
        return false;
      }
    } else {
      state.imgLoading = false;
      state.empty = true;
      ElMessage.warning('楼层数据为空');
      return false;
    }
  } catch (error) {
    state.imgLoading = false;
    state.empty = true;
    ElMessage.error('获取楼层数据失败');
    return false;
  }
}

/**
 * 初始化画布
 */
const initCanvas = async (url: string) => {
  try {
    if (!url) {
      ElMessage.error('背景图URL为空');
      state.imgLoading = false;
      state.empty = true;
      return;
    }

    if (map?.canvas) {
      try {
        map.canvas.dispose();
        map.canvas = undefined as any;
      } catch (e) {
        console.warn('画布销毁失败，继续初始化新画布');
      }
    }
    
    await drawCanvas();
    
    if (!map?.canvas) {
      ElMessage.error('画布未正确初始化');
      state.imgLoading = false;
      state.empty = true;
      return;
    }
    
    map.setBgImage(url, () => {
      state.imgLoading = false;
      state.empty = false;
      
      if (map?.canvas) {
        try {
          map.canvas.renderAll();
        } catch (renderError) {
          console.error('画布渲染失败');
        }
      }
      
      if (!checkScaleAndOrigin()) {
        ElMessage.warning('比例尺和原点配置检查失败，背景图已显示，但无法渲染区域');
        return;
      }
      
      if (DrawingState.areaTree?.length > 0) {
        try {
          setAreaTree(DrawingState.areaTree);
        } catch (areaError) {
          ElMessage.warning('区域数据渲染失败，请刷新页面重试');
        }
      }
    });
  } catch (error) {
    state.imgLoading = false;
    state.empty = true;
    ElMessage.error('初始化地图失败，请刷新页面重试');
  }
}

/**
 * 检查比例尺和原点配置
 */
function checkScaleAndOrigin(): boolean {
  if (!state.floorData.position) {
    ElMessage.warning('请先返回厂区管理设置比例尺和原点坐标');
    return false;
  }
  if (!state.imgInfoSize.scaleSize) {
    ElMessage.warning('请先设置比例尺');
    return false;
  }
  if (!state.imgInfoSize.orginMx || !state.imgInfoSize.orginMy) {
    ElMessage.warning('请先设置原点坐标');
    return false;
  }
  return true;
}

/**
 * 创建画布实例
 */
const drawCanvas = async () => {
  try {
    await nextTick();
    
    const scaleContainer = canvasParent.value;
    if (!scaleContainer) {
      throw new Error('画布容器不存在');
    }
    
    const screenWidth = scaleContainer.clientWidth;
    const screenHeight = scaleContainer.clientHeight;
    
    if (!screenWidth || !screenHeight || screenWidth <= 0 || screenHeight <= 0) {
      throw new Error('无效的画布尺寸');
    }
    
    state.screenInfo = {
      screenWidth,
      screenHeight,
      devicePixelRatio: window.devicePixelRatio || 1
    };
    
    const canvasElement = document.getElementById(state.canvasId);
    if (!canvasElement) {
      throw new Error('Canvas元素不存在');
    }

    map = new FabricCanvas({
      containerId: state.canvasId,
      width: screenWidth,
      height: screenHeight
    });
    
    if (!map?.canvas) {
      throw new Error('FabricCanvas实例创建失败');
    }
    
    initMapEventListeners();
  } catch (error) {
    state.imgLoading = false;
    state.empty = true;
    throw error;
  }
}

/**
 * 初始化地图事件监听
 */
const initMapEventListeners = () => {
  if (!map?.canvas) return;

  let clientPoint = { x: 0, y: 0 };
  const handleEvent = EventHandlerUtils.createEventThrottle();

  // 绘制动作映射
  const drawActions = {
    'circle': () => {
      if (!map?.canvas) return;
      map.canvas.isDragging = false;
      DrawingState.isMouseDown = true;
      DrawingState.areaObject = ObjectPool.acquire('circle', () => {
        const extendedMap = extendFabricCanvas(map);
        return (extendedMap as any).addCircleFence(DrawingState.startPoint, 0);
      });
      if (DrawingState.areaObject) {
        DrawingState.areaObject.set({ objectCaching: false });
      }
    },
    'rectangle': () => {
      if (!map?.canvas) return;
      map.canvas.isDragging = false;
      DrawingState.isMouseDown = true;
      DrawingState.areaObject = ObjectPool.acquire('rectangle', () => {
        const extendedMap = extendFabricCanvas(map);
        return (extendedMap as any).addRectangleFence(DrawingState.startPoint, 0, 0);
      });
      if (DrawingState.areaObject) {
        DrawingState.areaObject.set({ objectCaching: false });
      }
    },
    'polygon': () => {
      if (!map?.canvas) return;
      
      // 初始化多边形绘制器
      if (!DrawingState.polygonDrawer) {
        const fenceFactory = new FenceFactory(map.canvas);
        DrawingState.polygonDrawer = new RealtimePolygonDrawer(map.canvas, fenceFactory);
        
        // 设置完成回调
        DrawingState.polygonDrawer.setOnCompleteCallback((points) => {
          console.log('多边形绘制完成回调，点数:', points.length);
          // 注意：这里不需要再次调用 renderPolygon，因为 finishDrawing 已经创建了多边形
          // 只需要设置 areaObject 的引用
        });
        
        DrawingState.polygonDrawer.startDrawing();
      }
      
      // 添加点
      if (DrawingState.polygonDrawer && !DrawingState.polygonDrawer.isCurrentlyDrawing()) {
        DrawingState.polygonDrawer.startDrawing();
      }
      
      if (DrawingState.polygonDrawer) {
        DrawingState.polygonDrawer.addPoint(DrawingState.startPoint);
        console.log('添加多边形点:', DrawingState.startPoint, '当前点数:', DrawingState.polygonDrawer.getPointCount());
      }
    }
  };

  try {
    // 鼠标按下事件
    map.canvas.on('mouse:down', (opt: any) => {
      handleEvent(() => {
        opt.e.preventDefault();
        DrawingState.startPoint = opt.absolutePointer;
        clientPoint = opt.absolutePointer;
        
        if (DrawingState.drawType && DrawingState.drawType !== 'none') {
          map.canvas.isDragging = true;
          map.canvas.selection = false;
          
          const action = drawActions[DrawingState.drawType];
          if (action) {
            action();
            if (DrawingState.areaObject) {
              DrawingState.areaObject.set({ id: DrawingState.areaData.id });
              DrawingState.areaObject.set(setAreaColor(DrawingState.areaData, false, map));
              const renderState = throttledRender(map, state.lastRenderTime, state.fps, state.pendingRender);
              state.lastRenderTime = renderState.lastRenderTime;
              state.pendingRender = renderState.pendingRender;
            }
          }
          return;
        }
        
        if (opt.target?.id) {
          const clickedAreaId = opt.target.id;
          const isCurrentSelectedArea = currentSelectedAreaId.value === clickedAreaId;
          
          if (isCurrentSelectedArea && opt.target.selectable) {
            map.canvas.isDragging = false;
            map.canvas.selection = true;
            CanvasStateManager.bringToFrontAndUpdate(map, opt.target);
          } else if (opt.target.selectable) {
            map.canvas.isDragging = false;
            map.canvas.selection = true;
            CanvasStateManager.bringToFrontAndUpdate(map, opt.target);
          } else {
            map.canvas.isDragging = true;
            map.canvas.selection = false;
          }
          return;
        } else {
          map.canvas.isDragging = true;
          map.canvas.selection = false;
        }
      });
    });

    // 鼠标移动事件
    map.canvas.on('mouse:move', (opt: any) => {
      opt.e.preventDefault();
      const point = opt.absolutePointer;
      const dragDistance = EventHandlerUtils.calculateDragDistance(clientPoint, opt.e);
      const dragThreshold = EventHandlerUtils.getDynamicThreshold(map, 3, 5);
      
      switch (DrawingState.drawType) {
        case 'circle':
          if (DrawingState.isMouseDown && dragDistance > dragThreshold) {
            const extendedMap = extendFabricCanvas(map);
            (extendedMap as any).setCircleFence(DrawingState.areaObject, point, DrawingState.startPoint);
            
            // 实时显示圆形尺寸（提高更新频率）
            if (DrawingState.areaObject && DrawingState.areaData.id && Date.now() - state.lastRenderTime > 16) {
              // 移除旧的尺寸标注
              AreaDimensionManager.removeAreaDimensionMarkers(DrawingState.areaData.id as number, map);
              
              // 计算实际半径
              const realRadius = canvasToImageWithDPR(
                { x: DrawingState.areaObject.left, y: DrawingState.areaObject.top, radius: DrawingState.areaObject.radius },
                map, state.imgInfoSize, getScreenInfo()
              ).radius;
              
              // 创建临时区域数据用于显示尺寸
              const tempAreaData = {
                ...DrawingState.areaData,
                position: {
                  type: 'circle' as const,
                  points: {
                    x: canvasToImageWithDPR({ x: DrawingState.areaObject.left, y: DrawingState.areaObject.top }, map, state.imgInfoSize, getScreenInfo()).x,
                    y: canvasToImageWithDPR({ x: DrawingState.areaObject.left, y: DrawingState.areaObject.top }, map, state.imgInfoSize, getScreenInfo()).y,
                    radius: realRadius
                  }
                }
              };
              
              // 添加实时尺寸标注（支持编辑）
              DrawingState.dimensionMarkers = AreaDimensionManager.addAreaDimensionMarkers(
                tempAreaData,
                map,
                state.imgInfoSize,
                getScreenInfo(),
                true // 绘制过程中也显示完整的尺寸标注并支持编辑
              );
              DrawingState.showDimensions = true;
            }
            
            const renderState = throttledRender(map, state.lastRenderTime, state.fps, state.pendingRender);
            state.lastRenderTime = renderState.lastRenderTime;
            state.pendingRender = renderState.pendingRender;
          }
          break;
        case 'rectangle':
          if (DrawingState.isMouseDown && dragDistance > dragThreshold) {
            const extendedMap = extendFabricCanvas(map);
            (extendedMap as any).setRectangleFence(DrawingState.areaObject, point, DrawingState.startPoint);
            
            // 实时显示矩形尺寸（提高更新频率）
            if (DrawingState.areaObject && DrawingState.areaData.id && Date.now() - state.lastRenderTime > 16) {
              // 移除旧的尺寸标注
              AreaDimensionManager.removeAreaDimensionMarkers(DrawingState.areaData.id as number, map);
              
              // 计算实际尺寸
              const realDimensions = canvasToImageWithDPR(
                { 
                  x: DrawingState.areaObject.left, 
                  y: DrawingState.areaObject.top, 
                  width: DrawingState.areaObject.width, 
                  height: DrawingState.areaObject.height 
                },
                map, state.imgInfoSize, getScreenInfo()
              );
              
              // 创建临时区域数据用于显示尺寸
              const tempAreaData = {
                ...DrawingState.areaData,
                position: {
                  type: 'rectangle' as const,
                  points: {
                    x: realDimensions.x,
                    y: realDimensions.y,
                    width: realDimensions.width,
                    height: realDimensions.height
                  }
                }
              };
              
              // 添加实时尺寸标注（支持编辑）
              DrawingState.dimensionMarkers = AreaDimensionManager.addAreaDimensionMarkers(
                tempAreaData,
                map,
                state.imgInfoSize,
                getScreenInfo(),
                true // 绘制过程中也显示完整的尺寸标注并支持编辑
              );
              DrawingState.showDimensions = true;
            }
            
            const renderState = throttledRender(map, state.lastRenderTime, state.fps, state.pendingRender);
            state.lastRenderTime = renderState.lastRenderTime;
            state.pendingRender = renderState.pendingRender;
          }
          break;
        case 'polygon':
          // 使用实时多边形绘制器更新鼠标位置
          if (DrawingState.polygonDrawer && DrawingState.polygonDrawer.isCurrentlyDrawing()) {
            DrawingState.polygonDrawer.updateMousePosition(point);
          }
          break;
      }
    });

    // 鼠标释放事件
    map.canvas.on('mouse:up', (opt: any) => {
      opt.e.preventDefault();
      
      const totalDragDistance = EventHandlerUtils.calculateDragDistance(clientPoint, opt.e);
      const minDrawDistance = EventHandlerUtils.getDynamicThreshold(map, 5, 8);
      
      if (DrawingState.isMouseDown && (DrawingState.drawType == 'rectangle' || DrawingState.drawType == 'circle')) {
        if (totalDragDistance >= minDrawDistance) {
          drawDone();
        } else {
          if (DrawingState.areaObject) {
            map.canvas.remove(DrawingState.areaObject);
            DrawingState.areaObject = null;
          }
          ElMessage.info('绘制距离太小，已取消绘制');
        }
      }
      
      if (DrawingState.drawType == 'polygon' && DrawingState.polygonDrawer && DrawingState.polygonDrawer.isCurrentlyDrawing()) {
        // 添加新点到多边形绘制器
        DrawingState.polygonDrawer.addPoint(opt.absolutePointer);
        console.log('鼠标释放：添加多边形点', opt.absolutePointer, '当前点数:', DrawingState.polygonDrawer.getPointCount());
      }
      
      DrawingState.isMouseDown = false;
      map.canvas.isDragging = false;
      map.canvas.selection = false;
      
      if (opt.target?.id) {
        const areaData = findAreaNodeById(DrawingState.areaTree, opt.target.id);
        if (areaData) {
          handleUpdateObjectPosition(opt.target, areaData);
          setTimeout(() => CanvasStateManager.updateAllObjectCoordinates(map), 50);
        }
      }
    });

    // 双击事件
    map.canvas.on('mouse:dblclick', (opt: any) => {
      opt.e.preventDefault();
      if (DrawingState.drawType == 'polygon' && DrawingState.polygonDrawer && DrawingState.polygonDrawer.isCurrentlyDrawing()) {
        const pointCount = DrawingState.polygonDrawer.getPointCount();
        if (pointCount < 3) {
          ElMessage.warning('请至少绘制3个点');
          return;
        }
        
        console.log('双击完成多边形绘制，当前点数:', pointCount);
        // 完成多边形绘制 - finishDrawing 会自动创建最终的多边形对象
        const result = DrawingState.polygonDrawer.finishDrawing();
        if (result) {
          // 直接使用返回的多边形对象
          DrawingState.areaObject = result.polygon;
          
          if (DrawingState.areaObject) {
            DrawingState.areaObject.set({ 
              id: DrawingState.areaData.id,
              objectCaching: false 
            });
            DrawingState.areaObject.set(setAreaColor(DrawingState.areaData, false, map));
            // 完成绘制
            drawDone();
          } else {
            console.error('多边形对象创建失败');
          }
        }
      }
    });

    // 🔧 新增：监听区域移动事件，实时更新尺寸标注
    // @ts-ignore - 自定义事件
    map.canvas.on('area:moving', (e: any) => {
      const { target, areaId } = e;
      if (currentSelectedAreaId.value === areaId && DrawingState.showDimensions) {
        // 实时更新尺寸标注位置
        updateDimensionMarkersForMovingArea(target, areaId);
      }
    });

    // 🔧 新增：监听区域移动完成事件
    // @ts-ignore - 自定义事件
    map.canvas.on('area:moved', (e: any) => {
      const { target, areaId } = e;
      if (currentSelectedAreaId.value === areaId && DrawingState.showDimensions) {
        // 移动完成后更新尺寸标注
        const areaData = findAreaNodeById(DrawingState.areaTree, areaId);
        if (areaData) {
          // 更新区域数据
          handleUpdateObjectPosition(target, areaData);
        }
      }
    });

    // 🔧 新增：监听区域尺寸编辑事件
    // @ts-ignore - 自定义事件
    map.canvas.on('area:dimension:changed', (e: any) => {
      const { areaData, dimensionType, newValue, markers } = e;
      console.log('区域尺寸已通过文本编辑更改:', { areaData, dimensionType, newValue });
      
      // 更新区域树中的数据
      updateAreaTreeNode(DrawingState.areaTree, areaData.id as number, areaData);
      
      // 更新当前选中的区域数据和尺寸标注
      if (currentSelectedAreaId.value === areaData.id) {
        DrawingState.areaData = { ...areaData };
        DrawingState.dimensionMarkers = markers || [];
        DrawingState.showDimensions = true;
      }
      
      // 重新计算区域中心点
      const center = getAreaCenter(areaData.position as AreaPosition);
      if (center && !isNaN(center.x) && !isNaN(center.y)) {
        areaData.centerPoint = center;
      }
      
      // 通知父组件位置数据已更改
      const areaDataCopy = {
        ...areaData,
        position: JSON.parse(JSON.stringify(areaData.position)),
        centerPoint: areaData.centerPoint ? JSON.parse(JSON.stringify(areaData.centerPoint)) : null
      };
      emit('positionChanged', areaDataCopy);
      
      // 确保画布渲染
      nextTick(() => {
        map.canvas.renderAll();
      });
    });

    // 🔧 新增：监听画布缩放事件，更新尺寸标注位置
    // @ts-ignore - 自定义缩放事件
    map.canvas.on('zoom:changed', () => {
      // 使用节流避免频繁更新
      if (zoomUpdateTimeout) {
        clearTimeout(zoomUpdateTimeout);
      }
      
              zoomUpdateTimeout = setTimeout(() => {
          // 如果有选中的区域且显示尺寸标注，更新尺寸标注位置
          if (currentSelectedAreaId.value && DrawingState.showDimensions && DrawingState.areaData?.id) {
            console.log('画布缩放变化，更新尺寸标注位置');
            
            // 使用高效的更新方法，而不是重新创建
            if (DrawingState.areaData.position?.type && DrawingState.areaData.position.type !== 'polygon') {
              DrawingState.dimensionMarkers = AreaDimensionManager.updateAreaDimensionMarkers(
                DrawingState.areaData,
                map,
                state.imgInfoSize,
                getScreenInfo(),
                true, // 选中状态显示完整的尺寸标注
                false // 不强制重新创建，只更新位置
              );
            }
            
            // 立即渲染
            map.canvas.renderAll();
          }
          zoomUpdateTimeout = null;
        }, 50) as unknown as number; // 50ms 节流
    });

  } catch (error) {
    console.error('添加地图事件监听器失败:', error);
  }
}

// ================================= 绘制操作 ===========================
const addFence = (date: areaPostion) => {
  return createFence(date as AreaPosition, map, state.imgInfoSize, getScreenInfo());
}

// 绘制完成转换单位保存
const drawDone = () => {
  const currentType = DrawingState.drawType;
  const typeNames = { 'polygon': '多边形', 'rectangle': '矩形', 'circle': '圆形' };

  ElMessage.success(`绘制${typeNames[currentType] || ''}完成`);
  
  try {
    if (!DrawingState.areaData) return;
    
    if (!DrawingState.areaData.position) {
      DrawingState.areaData.position = { type: '', points: [] };
    }
    
    DrawingState.areaData.position.type = currentType;
    
    if (DrawingState.areaObject) {
      setAreaHighlightAndDraggable(DrawingState.areaObject, DrawingState.areaData, true, true, false, map);
      currentSelectedAreaId.value = DrawingState.areaData.id as number;
      map.canvas.renderAll();
    }

    handleDrawingComplete();
    
    // 清理绘制过程中的临时尺寸标注
    if (DrawingState.areaData.id) {
      AreaDimensionManager.removeAreaDimensionMarkers(DrawingState.areaData.id as number, map);
    }
    
    // 添加最终的尺寸标注（多边形除外）
    if (DrawingState.areaData.id && DrawingState.areaData.position && currentType !== 'polygon') {
      DrawingState.dimensionMarkers = AreaDimensionManager.addAreaDimensionMarkers(
        DrawingState.areaData,
        map,
        state.imgInfoSize,
        getScreenInfo(),
        true // 绘制完成后显示完整的尺寸标注并支持编辑
      );
      DrawingState.showDimensions = true;
    } else if (currentType === 'polygon') {
      // 多边形不显示尺寸标注
      DrawingState.dimensionMarkers = [];
      DrawingState.showDimensions = false;
    }
    
    if (DrawingState.areaData.id) {
      const areaDataCopy = {
        ...DrawingState.areaData,
        position: JSON.parse(JSON.stringify(DrawingState.areaData.position)),
        centerPoint: DrawingState.areaData.centerPoint ? JSON.parse(JSON.stringify(DrawingState.areaData.centerPoint)) : null
      };
      emit('positionChanged', areaDataCopy);
    }
  } catch (error) {
    ElMessage.error('绘制处理出错，请尝试重新绘制');
  } finally {
    DrawingState.drawType = 'none';
    DrawingState.isMouseDown = false;
    DrawingState.startPoint = null;
    DrawingStateManager.restoreAreasSelectionState(map, currentSelectedAreaId.value);
  }
}

/**
 * 处理绘制完成事件
 */
const handleDrawingComplete = () => {
  if (!DrawingState.areaObject || !DrawingState.areaData) return;
  
  const currentType = DrawingState.areaData.position?.type;
  if (!currentType) return;
  
  switch (currentType) {
    case 'polygon':
      if (!DrawingState.areaObject?.points?.length) return;
      
      const polygonPoints = DrawingState.areaObject.points.map((item: any) => {
        if (!item || typeof item.x !== 'number' || typeof item.y !== 'number') {
          return { x: 0, y: 0 };
        }
        return canvasToImageWithDPR(item, map, state.imgInfoSize, getScreenInfo());
      });
      
      DrawingState.areaData.position.points = polygonPoints;
      break;
      
    case 'rectangle':
      if (!DrawingState.areaObject || 
          typeof DrawingState.areaObject.width !== 'number' || 
          typeof DrawingState.areaObject.height !== 'number') return;

      const rectResult = canvasToImageWithDPR(
        { 
          x: DrawingState.areaObject.left, 
          y: DrawingState.areaObject.top,
          width: DrawingState.areaObject.width,
          height: DrawingState.areaObject.height
        },
        map, state.imgInfoSize, getScreenInfo()
      );
      
      DrawingState.areaData.position.points = {
        x: rectResult.x,
        y: rectResult.y,
        width: rectResult.width,
        height: rectResult.height
      };
      break;
      
    case 'circle':
      if (!DrawingState.areaObject || typeof DrawingState.areaObject.radius !== 'number') return;
      
      if (DrawingState.areaObject.radius <= 0.01) {
        let actualRadius = DrawingState.areaObject.radius;
        
        if (DrawingState.areaObject.width && DrawingState.areaObject.height) {
          actualRadius = Math.min(DrawingState.areaObject.width, DrawingState.areaObject.height) / 2;
        }
        
        if (actualRadius <= 0.01) {
          actualRadius = 1;
        }
        
        DrawingState.areaObject.radius = actualRadius;
      }
      
      const circleResult = canvasToImageWithDPR(
        { 
          x: DrawingState.areaObject.left, 
          y: DrawingState.areaObject.top,
          radius: DrawingState.areaObject.radius
        },
        map, state.imgInfoSize, getScreenInfo()
      );
      
      if (isNaN(circleResult.x) || isNaN(circleResult.y) || isNaN(circleResult.radius)) return;
      if (circleResult.radius <= 0) return;
      
      DrawingState.areaData.position = {
        type: 'circle',
        points: {
          x: circleResult.x,
          y: circleResult.y,
          radius: circleResult.radius
        }
      };
      break;
      
    default:
      return;
  }
  
  const center = getAreaCenter(DrawingState.areaData.position as AreaPosition);
  if (center && !isNaN(center.x) && !isNaN(center.y)) {
    DrawingState.areaData.centerPoint = center;
  }
  
  updateAreaTreeNode(DrawingState.areaTree, DrawingState.areaData.id as number, DrawingState.areaData);
  map.canvas.renderAll();
  emit('positionChanged', DrawingState.areaData);
}

// 重置时回收对象
const resetState = () => {
  if (DrawingState.areaObject) {
    ObjectPool.release(DrawingState.drawType, DrawingState.areaObject);
  }
  
  // 清理多边形绘制器
  if (DrawingState.polygonDrawer) {
    DrawingState.polygonDrawer.cancelDrawing();
    DrawingState.polygonDrawer = null;
  }
  
  // 清理绘制过程中的临时尺寸标注
  if (DrawingState.areaData?.id) {
    AreaDimensionManager.removeAreaDimensionMarkers(DrawingState.areaData.id as number, map);
    DrawingState.dimensionMarkers = [];
    DrawingState.showDimensions = false;
  }
  
  DrawingState.drawType = 'none';
  DrawingState.isMouseDown = false;
  DrawingState.startPoint = null;
  DrawingState.areaObject = null;
}

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  ObjectPool.clear();
  
  // 清理多边形绘制器
  if (DrawingState.polygonDrawer) {
    DrawingState.polygonDrawer.destroy();
    DrawingState.polygonDrawer = null;
  }
  
  // 清理缩放更新定时器
  if (zoomUpdateTimeout) {
    clearTimeout(zoomUpdateTimeout);
    zoomUpdateTimeout = null;
  }
});

/**
 * 绘制操作处理函数
 */
const handleDraw = (type: 'circle' | 'rectangle' | 'polygon' | 'clear') => {
  checkScaleAndOrigin();
  
  if (DrawingState.areaData?.position?.points) {
    removeLastPoint();
  }
  
  if (!DrawingState.areaData) {
    DrawingState.areaData = {} as AreaNode;
  }
  
  if (!DrawingState.areaData.position) {
    DrawingState.areaData.position = { type: '', points: [] };
  }
  
  switch (type) {
    case 'circle':
      DrawingState.drawType = 'circle';
      DrawingStateManager.disableAllAreasForDrawing(map);
      break;
    case 'rectangle':
      DrawingState.drawType = 'rectangle';
      DrawingStateManager.disableAllAreasForDrawing(map);
      break;
    case 'polygon':
      DrawingState.drawType = 'polygon';
      DrawingStateManager.disableAllAreasForDrawing(map);
      // 重置多边形绘制器
      if (DrawingState.polygonDrawer) {
        DrawingState.polygonDrawer.cancelDrawing();
        DrawingState.polygonDrawer = null;
      }
      break;
    case 'clear':
      removeLastPoint();
      DrawingState.drawType = 'none';
      DrawingStateManager.restoreAreasSelectionState(map, currentSelectedAreaId.value);
      break;
  }
}

/**
 * 实时更新移动中区域的尺寸标注位置
 * @param target 移动的区域对象
 * @param areaId 区域ID
 */
const updateDimensionMarkersForMovingArea = (target: any, areaId: number) => {
  if (!map?.canvas || !target) return;
  
  try {
    // 获取当前区域的所有尺寸标注相关对象
    const dimensionObjects = map.canvas.getObjects().filter((obj: any) => 
      obj.id && typeof obj.id === 'string' && (
        obj.id === `area-dimension-${areaId}` ||
        obj.id === `area-center-${areaId}` ||
        obj.id === `area-radius-line-${areaId}`
      )
    );
    
    // 根据区域类型更新尺寸标注位置
    const currentZoom = map.canvas.getZoom();
    
    dimensionObjects.forEach((dimObj: any) => {
      // 🔧 强制确保尺寸文本可见
      dimObj.set({
        visible: true,
        opacity: 1,
        objectCaching: false,
        noScaleCache: true
      });
      
      if (target.type === 'circle') {
        // 圆形区域：更新圆心标记、半径线和文本位置
        if (dimObj.id === `area-center-${areaId}`) {
          // 更新圆心标记位置
          dimObj.set({
            left: target.left,
            top: target.top,
            scaleX: 1 / currentZoom,
            scaleY: 1 / currentZoom
          });
        } else if (dimObj.id === `area-radius-line-${areaId}`) {
          // 更新半径线位置
          const endX = target.left + target.radius;
          const endY = target.top;
          const extendedMap = extendFabricCanvas(map);
          (extendedMap as any).updateRadiusLine(
            dimObj,
            { x: target.left, y: target.top },
            { x: endX, y: endY }
          );
          dimObj.set({
            strokeWidth: 1 / currentZoom,
            strokeDashArray: [3 / currentZoom, 2 / currentZoom]
          });
        } else {
          // 更新半径文本位置 - 在圆形右边
          const visualOffset = 10;
          const actualOffset = visualOffset / currentZoom;
          const textX = target.left + target.radius + actualOffset;
          const textY = target.top;
          dimObj.set({
            left: textX,
            top: textY,
            fontSize: 14 / currentZoom,
            padding: 3 / currentZoom
          });
        }
      } else if (target.type === 'rect') {
        // 矩形区域：根据文本类型更新位置
        if (dimObj.originalAreaInfo?.textType === 'width') {
          const visualOffset = 10;
          const actualOffset = visualOffset / currentZoom;
          dimObj.set({
            left: target.left + (target.width * (target.scaleX || 1)) / 2,
            top: target.top - actualOffset,
            fontSize: 14 / currentZoom,
            padding: 3 / currentZoom
          });
        } else if (dimObj.originalAreaInfo?.textType === 'height') {
          const visualOffset = 10;
          const actualOffset = visualOffset / currentZoom;
          dimObj.set({
            left: target.left - actualOffset,
            top: target.top + (target.height * (target.scaleY || 1)) / 2,
            fontSize: 14 / currentZoom,
            padding: 3 / currentZoom
          });
        }
      } else if (target.type === 'polygon') {
        // 多边形区域：隐藏尺寸标注
        dimObj.set({
          visible: false
        });
      }
      
      // 🔧 强制更新对象坐标和设置dirty标志
      dimObj.setCoords();
      dimObj.dirty = true;
    });
    
    // 🔧 使用requestAnimationFrame确保渲染在下一帧执行
    requestAnimationFrame(() => {
      map.canvas.renderAll();
    });
  } catch (error) {
    console.error('更新移动中区域的尺寸标注失败:', error);
    // 🔧 错误时尝试重新创建尺寸标注
    try {
      const areaData = findAreaNodeById(DrawingState.areaTree, areaId);
      if (areaData && DrawingState.showDimensions) {
        AreaDimensionManager.updateAreaDimensionMarkers(
          areaData,
          map,
          state.imgInfoSize,
          getScreenInfo(),
          true,
          true // 强制重新创建
        );
      }
    } catch (recreateError) {
      console.error('重新创建尺寸标注失败:', recreateError);
    }
  }
};

/**
 * 处理区域关闭事件
 */
const handleAreaClose = (area) => {
  if (!map?.canvas) return;
  
  CanvasStateManager.clearActiveState(map);
  
  if (!area?.id) {
    currentSelectedAreaId.value = null;
    DrawingState.areaData = {} as AreaNode;
    // 移除所有尺寸标注
    AreaDimensionManager.removeAllAreaDimensionMarkers(map);
    DrawingState.dimensionMarkers = [];
    DrawingState.showDimensions = false;
    CanvasStateManager.resetAllAreasToNormal(map, DrawingState.areaTree);
    map.canvas.renderAll();
    return;
  }
  
  if (currentSelectedAreaId.value === area.id) {
    currentSelectedAreaId.value = null;
    DrawingState.areaData = {} as AreaNode;
    // 移除该区域的尺寸标注
    AreaDimensionManager.removeAreaDimensionMarkers(area.id, map);
    DrawingState.dimensionMarkers = [];
    DrawingState.showDimensions = false;
  }
  
  const currentArea = map.findGroupById(area.id as any);
  if (currentArea) {
    setAreaHighlightAndDraggable(currentArea, area, false, false, false, map);
    map.canvas.renderAll();
  }
}

/**
 * 移除上一个绘制的图形
 */
const removeLastPoint = () => {
  if (!map?.canvas || !DrawingState.areaData?.id) return;
  
  // 移除尺寸标注（包括绘制过程中的临时标注）
  AreaDimensionManager.removeAreaDimensionMarkers(DrawingState.areaData.id as number, map);
  DrawingState.dimensionMarkers = [];
  DrawingState.showDimensions = false;
  
  // 如果正在绘制多边形，取消绘制
  if (DrawingState.polygonDrawer && DrawingState.polygonDrawer.isCurrentlyDrawing()) {
    DrawingState.polygonDrawer.cancelDrawing();
    DrawingState.polygonDrawer = null;
  }
  
  // 移除绘制中的区域对象
  if (DrawingState.areaObject) {
    map.canvas.remove(DrawingState.areaObject);
    DrawingState.areaObject = null;
  }
  
  const area = map.findGroupById(DrawingState.areaData.id as any);
  if (area) {
    map.canvas.remove(area);
    DrawingState.areaData.position = { type: '', points: [] };
    resetState();
    map.canvas.requestRenderAll();
  }
  
  DrawingStateManager.restoreAreasSelectionState(map, currentSelectedAreaId.value);
}

/**
 * 设置单个区域数据并高亮显示
 */
const setAreaData = (data: AreaNode) => {
  if (!data) return;
  
  if (data.position && typeof data.position === 'string') {
    try {
      data.position = JSON.parse(data.position);
    } catch (e) {
      data.position = { type: '', points: [] };
    }
  }
  
  if (!data.position?.type) {
    data.position = { type: '', points: [] };
  }
  
  if (map?.canvas) {
    CanvasStateManager.clearActiveState(map);
    CanvasStateManager.resetAllAreasToNormal(map, DrawingState.areaTree);
    
    // 移除所有区域的尺寸标注
    AreaDimensionManager.removeAllAreaDimensionMarkers(map);
    
    const currentArea = map.findGroupById(data.id as any);
    if (currentArea) {
      setAreaHighlightAndDraggable(currentArea, data, true, true, false, map);
      CanvasStateManager.bringToFrontAndUpdate(map, currentArea);
      
      // 为选中的区域添加尺寸标注（多边形除外）
      if (data.position?.type && data.position.type !== 'none' && data.position.type !== 'polygon') {
        DrawingState.dimensionMarkers = AreaDimensionManager.addAreaDimensionMarkers(
          data,
          map,
          state.imgInfoSize,
          getScreenInfo(),
          true // 选中状态显示完整的尺寸标注
        );
        DrawingState.showDimensions = true;
      } else {
        // 多边形或无效类型不显示尺寸标注
        DrawingState.dimensionMarkers = [];
        DrawingState.showDimensions = false;
      }
      
      setTimeout(() => CanvasStateManager.updateAllObjectCoordinates(map), 10);
    }
    
    currentSelectedAreaId.value = data.id as number;
    DrawingState.areaData = JSON.parse(JSON.stringify(data));
    map.canvas.renderAll();
  } else {
    DrawingState.areaData = JSON.parse(JSON.stringify(data));
    currentSelectedAreaId.value = data.id as number;
  }
}

/**
 * 设置多个区域数据并高亮显示（多选模式）
 */
const setAreaDataList = (data: AreaNode[]) => {
  DrawingState.isMultiple = data.length > 0;
  
  if (map.canvas && DrawingState.isMultiple) {
    CanvasStateManager.clearActiveState(map);
    CanvasStateManager.resetAllAreasToNormal(map, DrawingState.areaTree);
    
    // 移除所有区域的尺寸标注
    AreaDimensionManager.removeAllAreaDimensionMarkers(map);
    
    data.forEach(area => {
      if (area.position && typeof area.position === 'string') {
        area.position = JSON.parse(area.position);
      }
      
      const currentArea = map.findGroupById(area.id as any);
      if (currentArea) {
        const isCurrentSelected = currentSelectedAreaId.value === area.id;
        
        if (isCurrentSelected) {
          setAreaHighlightAndDraggable(currentArea, area, true, true, false, map);
          
          // 为当前选中的区域添加尺寸标注（多边形除外）
          if (area.position?.type && area.position.type !== 'none' && area.position.type !== 'polygon') {
            DrawingState.dimensionMarkers = AreaDimensionManager.addAreaDimensionMarkers(
              area,
              map,
              state.imgInfoSize,
              getScreenInfo(),
              true // 选中状态显示完整的尺寸标注
            );
            DrawingState.showDimensions = true;
          } else {
            // 多边形或无效类型不显示尺寸标注
            DrawingState.dimensionMarkers = [];
            DrawingState.showDimensions = false;
          }
        } else {
          setAreaHighlightAndDraggable(currentArea, area, true, false, true, map);
        }
      }
    });
    
    map.canvas.renderAll();
  } else {
    if (map?.canvas) {
      CanvasStateManager.clearActiveState(map);
      // 移除所有区域的尺寸标注
      AreaDimensionManager.removeAllAreaDimensionMarkers(map);
      DrawingState.dimensionMarkers = [];
      DrawingState.showDimensions = false;
    }
    
    CanvasStateManager.resetAllAreasToNormal(map, DrawingState.areaTree);
    
    if (DrawingState.areaData?.id && currentSelectedAreaId.value) {
      const currentArea = map.findGroupById(currentSelectedAreaId.value as any);
      if (currentArea) {
        setAreaHighlightAndDraggable(currentArea, DrawingState.areaData, true, true, false, map);
        
        // 为当前选中的区域添加尺寸标注（多边形除外）
        if (DrawingState.areaData.position?.type && DrawingState.areaData.position.type !== 'none' && DrawingState.areaData.position.type !== 'polygon') {
          DrawingState.dimensionMarkers = AreaDimensionManager.addAreaDimensionMarkers(
            DrawingState.areaData,
            map,
            state.imgInfoSize,
            getScreenInfo(),
            true // 选中状态显示完整的尺寸标注
          );
          DrawingState.showDimensions = true;
        } else {
          // 多边形或无效类型不显示尺寸标注
          DrawingState.dimensionMarkers = [];
          DrawingState.showDimensions = false;
        }
      }
    }
    
    if (map?.canvas) {
      map.canvas.renderAll();
    }
  }
}

// 数据增加时更新区域树
const setAreaTree = async (data: AreaNode[]) => {
  try {
    if (!data?.length) return;

    DrawingState.areaTree = data;
    DrawingState.areaData = {} as AreaNode;
    
    if (!map?.canvas) return;
    
    if (map.canvas && map.canvas.backgroundImage) {
      const objects = map.canvas.getObjects();
      objects.forEach(item => {
        if (item) {
          map.canvas.remove(item);
        }
      });

      resetState();

      await Promise.all(
        data.map(areaNode =>
          renderArea(areaNode).catch(error => {
            console.error('渲染区域出错:', error);
          })
        )
      );

      map.canvas.renderAll();
    }
  } catch (error) {
    ElMessage.error('加载区域树失败');
  }
}

// 渲染区域
const renderArea = async (areaNode: AreaNode) => {
  if (!areaNode || !map?.canvas) return;

  try {
    if (areaNode.position && typeof areaNode.position === 'string') {
      areaNode.position = JSON.parse(areaNode.position);
    }

    if (areaNode.position?.type && areaNode.position.type !== 'none') {
      const fence = addFence(areaNode.position);
      if (fence) {
        fence.set({
          id: areaNode.id,
          objectCaching: false
        });
        
        fence.set(setAreaColor(areaNode, currentSelectedAreaId.value === areaNode.id, map));
        
        const isCurrentSelected = currentSelectedAreaId.value === areaNode.id;
        setAreaHighlightAndDraggable(
          fence, 
          areaNode, 
          isCurrentSelected,
          isCurrentSelected,
          false,
          map
        );
      }
    }

    if (areaNode.children?.length > 0) {
      await Promise.all(areaNode.children.map(child => renderArea(child)));
    }
  } catch (error) {
    console.error('渲染区域出错:', error, areaNode);
  }
}

// 优化批量渲染
const batchRender = (callback: () => void) => {
  if (!map?.canvas) return;

  map.canvas.renderOnAddRemove = false;
  callback();
  map.canvas.renderOnAddRemove = true;
  const renderState = throttledRender(map, state.lastRenderTime, state.fps, state.pendingRender);
  state.lastRenderTime = renderState.lastRenderTime;
  state.pendingRender = renderState.pendingRender;
}

// 优化事件监听器
let resizeTimeout: number | null = null;
const handleResize = () => {
  if (resizeTimeout) {
    clearTimeout(resizeTimeout);
  }

  resizeTimeout = setTimeout(() => {
    if (map?.canvas && !state.empty) {
      const scaleContainer = canvasParent.value;
      if (scaleContainer) {
        const screenWidth = scaleContainer.clientWidth;
        const screenHeight = scaleContainer.clientHeight;

        batchRender(() => {
          map.canvas.setWidth(screenWidth);
          map.canvas.setHeight(screenHeight);
        });
      }
    }
    resizeTimeout = null;
  }, 200) as unknown as number;
}

// 添加窗口大小变化的响应式处理
onMounted(() => {
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

/**
 * 清除所有选择状态
 */
const clearAllSelections = () => {
  currentSelectedAreaId.value = null;
  DrawingState.isMultiple = false;
  DrawingState.areaData = {} as AreaNode;
  
  // 移除所有尺寸标注
  if (map?.canvas) {
    AreaDimensionManager.removeAllAreaDimensionMarkers(map);
    DrawingState.dimensionMarkers = [];
    DrawingState.showDimensions = false;
    
    CanvasStateManager.clearActiveState(map);
    CanvasStateManager.resetAllAreasToNormal(map, DrawingState.areaTree);
    map.canvas.renderAll();
  }
}

defineExpose({
  handleAreaClose,
  setAreaDataList,
  handleDraw,
  getFloor,
  setAreaData,
  setAreaTree,
  clearAllSelections
});

// 更新对象位置的函数
const handleUpdateObjectPosition = (object: any, areaData: AreaNode) => {
  const updatedData = updateObjectPosition(
    object, 
    areaData, 
    map, 
    state.imgInfoSize, 
    getScreenInfo(),
    (data) => emit('positionChanged', data)
  );
  
  if (updatedData) {
    if (currentSelectedAreaId.value === updatedData.id) {
       updateAreaTreeNode(DrawingState.areaTree, updatedData.id as number, updatedData);
      DrawingState.areaData = { ...updatedData };
      
      // 更新尺寸标注（多边形除外）
      if (DrawingState.showDimensions && updatedData.position?.type && updatedData.position.type !== 'none' && updatedData.position.type !== 'polygon') {
        AreaDimensionManager.updateAreaDimensionMarkers(
          updatedData,
          map,
          state.imgInfoSize,
          getScreenInfo(),
          true // 拖动时保持选中状态的尺寸标注
        );
      }
    }
    
    if (map?.canvas) {
      map.canvas.renderAll();
    }
  }
}

// 添加对外暴露的事件
const emit = defineEmits(['positionChanged']);
</script>

<style scoped></style>
