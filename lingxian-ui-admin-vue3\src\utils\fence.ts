/**
 * 围栏管理相关的工具函数
 * 提供围栏绘制、坐标转换等常用功能
 */

import FabricCanvas from '@/utils/FabricCanvas'
import { canvasToImageWithDPR, imageToCanvasWithDPR, type ScreenInfo } from '@/store/modules/canvas/drawing'
import { Iposition } from '@/api/master/floor/type'
import { ElMessage } from 'element-plus'
import { AreaNode } from '@/api/master/area'
import {
  Circle,
  Rect,
  Polygon,
  Line,
  Group,
} from 'fabric'


// 围栏颜色配置接口
export interface FenceColor {
  color: string
  stroke: string
  strokeWidth: number
}

// 围栏数据类型定义
export interface FenceDate {
  type: 'circle' | 'rectangle' | 'polygon'
  points: any
}

// 区域位置接口定义
export interface CirclePoints {
  x: number
  y: number
  radius: number
}

export interface RectanglePoints {
  x: number
  y: number
  width: number
  height: number
}


/**
 * 围栏配置常量
 */
export const FENCE_CONFIG = {
  BASE_FONT_SIZE: 15,
  BASE_TEXT_PADDING: 4,
  BASE_STROKE_WIDTH: 2,
  BASE_POINT_RADIUS: 4,
  BASE_HANDLE_RADIUS: 35,
  MIN_VISIBLE_DISTANCE: 2,
  MIN_DISPLAY_DISTANCE: 0.001
} as const


export type PolygonPoints = Array<{ x: number, y: number }>

export interface AreaPosition {
  type: 'circle' | 'rectangle' | 'polygon' | 'none'
  points: CirclePoints | RectanglePoints | PolygonPoints
}

/**
 * 检查比例尺和原点配置
 * @param floorData 楼层数据
 * @param imgInfoSize 图片尺寸信息
 * @returns boolean 配置是否有效
 */
export function checkScaleAndOrigin(floorData: any, imgInfoSize: Iposition): boolean {
  if (!floorData.position) {
    ElMessage.warning('请先返回厂区管理设置比例尺和原点坐标')
    return false
  }
  // 检查比例尺配置
  if (!imgInfoSize.scaleSize) {
    ElMessage.warning('请先设置比例尺')
    return false
  }
  // 检查原点配置
  if (!imgInfoSize.orginMx || !imgInfoSize.orginMy) {
    ElMessage.warning('请先设置原点坐标')
    return false
  }
  return true
}

/**
 * 获取屏幕信息对象
 * @param move 移动和缩放信息
 * @param container 容器元素引用
 * @returns ScreenInfo 屏幕信息对象
 */
export function getScreenInfo(move: any, container: any): ScreenInfo {
  // 如果state.move中的值不合理，使用容器尺寸
  if (!move.screenWidth || !move.screenHeight) {
    if (container) {
      console.log('使用容器尺寸替代state中的屏幕尺寸')
      move.screenWidth = container.clientWidth
      move.screenHeight = container.clientHeight
      move.devicePixelRatio = window.devicePixelRatio || 1
    }
  }
  
  return {
    screenWidth: move.screenWidth,
    screenHeight: move.screenHeight,
    devicePixelRatio: move.devicePixelRatio
  }
}

/**
 * 添加围栏到画布
 * @param type 围栏类型
 * @param date 围栏数据
 * @param map FabricCanvas实例
 * @param imgInfoSize 图片尺寸信息
 * @param screenInfo 屏幕信息
 * @returns 围栏对象
 */
export function addFence(
  type: 'circle' | 'rectangle' | 'polygon', 
  date: FenceDate, 
  map: FabricCanvas,
  imgInfoSize: Iposition,
  screenInfo: ScreenInfo
) {
  let fenceType: any = null

  // 创建围栏工厂实例
  const fenceFactory = new FenceFactory(map.canvas)

  try {
    switch (type) {
      case 'circle':
        // 确保实际半径为正值
        const safeRealRadius = Math.max(date.points.radius || 0.001, 0.001)
        
        // 从实际坐标转换为画布坐标
        const { canvasX, canvasY, radius } = imageToCanvasWithDPR(
          { x: date.points.x, y: date.points.y, radius: safeRealRadius },
          map,
          imgInfoSize,
          screenInfo
        )
        
        // 确保画布半径为正值
        const safeCanvasRadius = Math.max(radius, 1)

        // 使用围栏工厂创建圆形围栏
        fenceType = fenceFactory.addCircleFence({ x: canvasX, y: canvasY }, safeCanvasRadius)
        break

      case 'rectangle':
        // 确保实际尺寸为正值
        const safeRealWidth = Math.max(date.points.width || 0.001, 0.001)
        const safeRealHeight = Math.max(date.points.height || 0.001, 0.001)
        
        // 从实际坐标转换为画布坐标
        const { canvasX: x, canvasY: y, width, height } = imageToCanvasWithDPR(
          { x: date.points.x, y: date.points.y, width: safeRealWidth, height: safeRealHeight },
          map,
          imgInfoSize,
          screenInfo
        )

        // 确保画布尺寸为正值
        const safeCanvasWidth = Math.max(width, 1)
        const safeCanvasHeight = Math.max(height, 1)

        // 使用围栏工厂创建矩形围栏
        fenceType = fenceFactory.addRectangleFence({ x, y }, safeCanvasWidth, safeCanvasHeight)
        break

      case 'polygon':
        // 转换多边形点坐标
        const polygonPoints = date.points as Array<{ x: number; y: number }>
        const canvasPoints = polygonPoints.map(point => {
          const result = imageToCanvasWithDPR(
            { x: point.x, y: point.y },
            map,
            imgInfoSize,
            screenInfo
          )
          return { x: result.canvasX, y: result.canvasY }
        })

        // 使用RealtimePolygonDrawer创建多边形围栏
        fenceType =  fenceFactory.createPolygonFence(canvasPoints)
        break
    }
    
    // 设置公共属性
    if (fenceType) {
      fenceType.set({
        id: 'fence',
        objectCaching: false,
        selectable: false,
        hasBorders: false,
        hasControls: false,
        perPixelTargetFind: true, // 保持边框宽度一致，不随缩放变化
        strokeUniform: true, // 保持边框宽度一致，不随缩放变化
        noScaleCache: false
      })
    }
    
    return fenceType
  } catch (error) {
    console.error('添加围栏失败:', error, '类型:', type, '数据:', date)
    return null
  }
}

/**
 * 设置围栏颜色和样式
 * @param data 区域数据
 * @param isHighlighted 是否高亮显示
 * @returns 围栏样式对象
 */
export function setFenceColor(data: AreaNode | any, isHighlighted: boolean = false) {
  // 兼容直接传色值
  let color = data.color
  // 兜底：如果不是合法色值，强制蓝色
  if (!/^#([0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/.test(color)) color = '#1e90ff'
  // 透明度处理
  const transparentColor = color + (isHighlighted ? '66' : '33')
  return {
    stroke: color,
    fill: transparentColor,
    strokeWidth: isHighlighted ? 2.5 : 1.5,
  }
}

/**
 * 清理围栏相关元素
 * @param map FabricCanvas实例
 */
export function cleanupFenceElements(map: FabricCanvas) {
  // 确保画布已初始化
  if (!map || !map.canvas) {
    return
  }
  
  // 查找并移除所有围栏相关对象
  const objectsToRemove = map.canvas.getObjects().filter((item: any) => 
    item.id === 'fence' || 
    item.id === 'circle-center' || 
    item.id === 'radius-line' || 
    item.id === 'radius-text' || 
    item.id === 'width-text' || 
    item.id === 'height-text' || 
    item.id === 'segment-text' ||
    (item.type === 'i-text' && (
      item.text.includes('radius') || 
      item.text.match(/^\d+\.?\d*$/) // 匹配纯数字文本
    ))
  )
  
  objectsToRemove.forEach((obj: any) => {
    map.canvas.remove(obj)
  })
  
  // 清理实时尺寸文本 - 直接从画布中删除所有i-text类型
  const textObjectsToRemove = map.canvas.getObjects().filter((item: any) => 
    item.type === 'i-text'
  );
  
  textObjectsToRemove.forEach((obj: any) => {
    map.canvas.remove(obj)
  });
  
  // 刷新画布
  map.canvas.requestRenderAll()
}



/**
 * 从画布坐标转换为围栏数据格式
 * @param type 围栏类型
 * @param fenceObj 围栏对象
 * @param map FabricCanvas实例
 * @param imgInfoSize 图片尺寸信息
 * @param screenInfo 屏幕信息
 * @returns 围栏数据
 */
export function convertToFenceData(
  type: 'circle' | 'rectangle' | 'polygon',
  fenceObj: any,
  map: FabricCanvas,
  imgInfoSize: Iposition,
  screenInfo: ScreenInfo
): FenceDate {
  const fenceDate: FenceDate = {
    type: type,
    points: null
  }
  
  switch (type) {
      
    case 'rectangle':
      // 获取矩形的实际尺寸（考虑缩放）
      const safeCanvasWidth = Math.max(fenceObj.getScaledWidth(), 1)
      const safeCanvasHeight = Math.max(fenceObj.getScaledHeight(), 1)

      // 使用坐标转换函数
      const { x, y, width, height } = canvasToImageWithDPR(
        { 
          x: fenceObj.left, 
          y: fenceObj.top, 
          width: safeCanvasWidth, 
          height: safeCanvasHeight
        },
        map,
        imgInfoSize,
        screenInfo
      )

      fenceDate.points = {
        x,
        y,
        width,
        height
      }
      break
      
    case 'circle':
      // 获取圆形的实际半径（考虑缩放）
      const safeCanvasRadius = Math.max(fenceObj.getScaledWidth() / 2, 1)

      // 使用坐标转换函数
      const { x: left, y: top, radius } = canvasToImageWithDPR(
        { 
          x: fenceObj.left, 
          y: fenceObj.top, 
          radius: safeCanvasRadius
        },
        map,
        imgInfoSize,
        screenInfo
      )

      // 确保实际半径也为正值
      const safeRadius = Math.max(radius, 0.001) // 最小实际半径（米）

      fenceDate.points = {
        x: left,
        y: top,
        radius: safeRadius
      }
      break

    case 'polygon':
      // 多边形坐标转换
      const polygonPoints = fenceObj.points || []
      const realPoints = polygonPoints.map((point: { x: number; y: number }) => {
        const realCoord = canvasToImageWithDPR(
          { x: point.x, y: point.y },
          map,
          imgInfoSize,
          screenInfo
        )
        return { x: realCoord.x, y: realCoord.y }
      })

      fenceDate.points = realPoints
      break
  }
  
  return fenceDate
}



/**
 * 可编辑围栏类
 * 支持通过点击文本编辑尺寸，围栏会实时更新
 * 
 * 注意：以下方法已弃用或冗余，保留是为了兼容性：
 * - updatePolygonSegment：逻辑已移至 updateFenceByDimension 方法
 */
export class EditableFence {
  private map: FabricCanvas
  private fenceFactory: FenceFactory
  private imgInfoSize: Iposition
  private screenInfo: ScreenInfo
  private fenceObject: any = null
  private centerMarker: Group | null = null
  private type: 'circle' | 'rectangle' | 'polygon'
  private id: string
  private originalCanvasSelection: boolean = true // 保存画布原始选择状态
  constructor(
    map: FabricCanvas,
    imgInfoSize: Iposition,
    screenInfo: ScreenInfo,
    type: 'circle' | 'rectangle' | 'polygon',
    id: string = 'editable-fence'
  ) {
    this.map = map
    this.fenceFactory = new FenceFactory(map.canvas)
    this.imgInfoSize = imgInfoSize
    this.screenInfo = screenInfo
    this.type = type
    this.id = id

    // 添加缩放监听器
    this.addZoomListener()
  }

  /**
   * 添加缩放监听器
   */
  private addZoomListener() {
    this.map.canvas.on('after:render', () => {
      this.updateElementsForZoom()
    })
  }

  /**
   * 根据缩放比例更新所有元素的大小和位置
   */
  private updateElementsForZoom() {
    const currentZoom = this.map.canvas.getZoom();
    const zoomFactor = 1 / currentZoom;
    
    // 更新围栏线宽 - 确保在高缩放下边框仍然可见
    if (this.fenceObject) {
      // 计算适当的边框宽度，确保在高缩放下仍然可见
      const minStrokeWidth = 1.5 / currentZoom; // 最小线宽，确保高缩放时仍可见
      const strokeWidth = Math.max(2 * zoomFactor, minStrokeWidth);
      
      this.fenceObject.set({
        strokeWidth: strokeWidth,
        strokeUniform: true // 保持边框宽度一致，不随缩放变化
      });
      
      // 确保围栏坐标更新
      this.fenceObject.setCoords();
    }
    

    
    // 更新圆心标记
    if (this.centerMarker) {
      // 调整线宽，确保在高缩放级别下仍然可见
      const minStrokeWidth = 0.5 / currentZoom;
      const strokeWidth = Math.max(1.5 * zoomFactor, minStrokeWidth);
      
      // 更新圆心十字标记的大小
      this.centerMarker.forEachObject((obj: any) => {
        if (obj.type === 'line') {
          obj.set({
            strokeWidth: strokeWidth,
            strokeUniform: true // 保持线宽一致，不随缩放变化
          });
        }
      });
      
      // 更新圆心标记位置
      if (this.fenceObject && this.type === 'circle') {
        this.centerMarker.set({
          left: this.fenceObject.left,
          top: this.fenceObject.top
        });
      }
      
      // 确保坐标更新
      this.centerMarker.setCoords();
    }
    
    // 强制重新渲染
    this.map.canvas.requestRenderAll();
  }

  /**
   * 创建圆形围栏
   */
  createCircleFence(center: { x: number; y: number }, radius: number) {
    this.type = 'circle'
    
    // 创建圆形围栏
    this.fenceObject = this.fenceFactory.addCircleFence(center, radius)
    this.fenceObject.set({
      id: this.id,
      selectable: true,
      evented: true,
      hasControls: true,
      hasBorders: true,
      lockRotation: true, // 锁定旋转
      lockUniScaling: true, // 锁定不均匀缩放，确保圆形保持圆形
      transparentCorners: false,
      cornerColor: '#409EFF',
      cornerSize: 8,
      cornerStyle: 'circle',
      borderColor: '#409EFF',
      borderScaleFactor: 1.5,
      strokeUniform: true, // 保持边框宽度一致，不随缩放变化
    })

    // 添加圆心标记
    this.addCircleCenter(center)

    // 添加围栏拖动事件监听
    this.addFenceDragEvents()
    
    this.map.canvas.renderAll()
    return this
  }

  /**
   * 创建矩形围栏
   */
  createRectangleFence(position: { x: number; y: number }, width: number, height: number) {
    this.type = 'rectangle'
    
    // 创建矩形围栏
    this.fenceObject = this.fenceFactory.addRectangleFence(position, width, height)
    this.fenceObject.set({
      id: this.id,
      selectable: true,
      evented: true,
      hasControls: true,
      hasBorders: true,
      lockRotation: true, // 锁定旋转
      lockUniScaling: false, // 允许不均匀缩放，可以单独调整宽高
      transparentCorners: false,
      cornerColor: '#409EFF',
      cornerSize: 8,
      cornerStyle: 'circle',
      borderColor: '#409EFF',
      borderScaleFactor: 1.5,
      strokeUniform: true, // 保持边框宽度一致，不随缩放变化
    })

    // 添加围栏拖动事件监听
    this.addFenceDragEvents()

    this.map.canvas.renderAll()
    return this
  }

  /**
   * 创建多边形围栏
   */
  createPolygonFence(points: { x: number; y: number }[]) {
    this.type = 'polygon'
    
    // 使用RealtimePolygonDrawer创建多边形围栏
    this.fenceObject = this.fenceFactory.createPolygonFence( points )
    
    if (this.fenceObject) {
      this.fenceObject.set({
        id: this.id,
        selectable: true,
        evented: true,
        hasControls: true,
        hasBorders: true,
        lockRotation: false, // 多边形允许旋转
        transparentCorners: false,
        cornerColor: '#409EFF',
        cornerSize: 8,
        cornerStyle: 'circle',
        borderColor: '#409EFF',
        borderScaleFactor: 1.5,
        strokeUniform: true, // 保持边框宽度一致，不随缩放变化
        noScaleCache: false,
        objectCaching: false
      })

      // 添加围栏拖动事件监听
      this.addFenceDragEvents()
    }

    this.map.canvas.renderAll()
    return this
  }

  /**
   * 添加圆心标记
   */
  private addCircleCenter(center: { x: number; y: number }) {
    const currentZoom = this.map.canvas.getZoom()
    const zoomFactor = 1 / currentZoom
    
    // 创建圆心十字标记
    const crossSize = 6 * zoomFactor
    
    const horizontalLine = new Line(
      [center.x - crossSize, center.y, center.x + crossSize, center.y],
      {
        stroke: '#ff0000',
        strokeWidth: 1.5 * zoomFactor,
        selectable: false,
        evented: false,
        objectCaching: false
      }
    )
    
    const verticalLine = new Line(
      [center.x, center.y - crossSize, center.x, center.y + crossSize],
      {
        stroke: '#ff0000',
        strokeWidth: 1.5 * zoomFactor,
        selectable: false,
        evented: false,
        objectCaching: false
      }
    )
    
    this.centerMarker = new Group([horizontalLine, verticalLine], {
      left: center.x,
      top: center.y,
      originX: 'center',
      originY: 'center',
      selectable: false,
      evented: false
    })
    
    this.map.canvas.add(this.centerMarker)
  }



















  /**
   * 获取围栏数据
   */
  getFenceData(): FenceDate {
    if (!this.fenceObject) {
      return { type: this.type, points: null }
    }
    
    return convertToFenceData(this.type, this.fenceObject, this.map, this.imgInfoSize, this.screenInfo)
  }

  /**
   * 销毁围栏及其所有相关元素
   */
  destroy() {
    // 移除围栏对象
    if (this.fenceObject) {
      this.map.canvas.remove(this.fenceObject)
    }

    // 移除圆心标记
    if (this.centerMarker) {
      this.map.canvas.remove(this.centerMarker)
    }

    // 恢复画布拖动功能
    this.restoreCanvasDragging()

    // 清空属性
    this.centerMarker = null
    this.fenceObject = null

    this.map.canvas.renderAll()
  }

  /**
   * 设置围栏样式
   */
  setStyle(style: { fill?: string; stroke?: string; strokeWidth?: number }) {
    if (this.fenceObject) {
      this.fenceObject.set(style)
      this.map.canvas.renderAll()
    }
  }

  /**
   * 获取围栏对象
   */
  getFenceObject() {
    return this.fenceObject
  }

  /**
   * 获取围栏工厂实例
   */
  getFenceFactory() {
    return this.fenceFactory
  }

  /**
   * 禁用画布拖动
   * 在围栏被拖动/调整大小时调用，防止画布一起移动
   */
  private disableCanvasDragging() {
    if (!this.map.canvas) return

    // 保存当前状态以便恢复
    this.originalCanvasSelection = this.map.canvas.selection || false

    // 禁用画布拖动和选择
    this.map.canvas.selection = false
    this.map.canvas.isDragging = false

    // 将鼠标样式改为可用状态
    document.body.style.cursor = 'auto'
  }

  /**
   * 恢复画布拖动
   * 在围栏拖动/调整结束后调用，恢复画布原有状态
   */
  private restoreCanvasDragging() {
    if (!this.map.canvas) return

    // 恢复原始状态
    this.map.canvas.selection = this.originalCanvasSelection

    // 恢复默认鼠标样式
    document.body.style.cursor = 'default'
  }

  /**
   * 添加围栏拖动事件监听
   * 在围栏被拖动时禁用画布拖动，拖动结束后恢复
   */
  private addFenceDragEvents() {
    if (!this.fenceObject) return

    // 监听围栏开始拖动事件
    this.fenceObject.on('mousedown', () => {
      // 禁用画布拖动
      this.disableCanvasDragging()
    })

    // 监听围栏拖动过程中的事件
    this.fenceObject.on('moving', () => {
      // 确保画布拖动保持禁用状态
      this.map.canvas.isDragging = false
    })

    // 监听围栏缩放过程中的事件
    this.fenceObject.on('scaling', () => {
      // 确保画布拖动保持禁用状态
      this.map.canvas.isDragging = false
    })

    // 监听围栏修改完成事件
    this.fenceObject.on('modified', () => {
      // 恢复画布拖动
      this.restoreCanvasDragging()
    })

    // 监听围栏失去选择状态
    this.fenceObject.on('deselected', () => {
      // 恢复画布拖动
      this.restoreCanvasDragging()
    })

    // 添加全局鼠标释放事件监听（防止modified事件未触发的情况）
    const handleGlobalMouseUp = () => {
      // 检查当前是否有活动对象，如果没有则恢复画布拖动
      const activeObject = this.map.canvas.getActiveObject()
      if (!activeObject || activeObject !== this.fenceObject) {
        this.restoreCanvasDragging()
      }
    }

    // 添加到画布上，确保只添加一次
    this.map.canvas.off('mouse:up', handleGlobalMouseUp) // 先移除可能存在的监听器
    this.map.canvas.on('mouse:up', handleGlobalMouseUp)
  }










}

/**
 * 围栏工厂类
 * 封装所有围栏创建和操作方法，从FabricCanvas中迁移而来
 */
export class FenceFactory {
  private canvas: any;
  private fenceColor: { color: string; stroke: string; strokeWidth: number };
  private polygonDrawing: boolean = false;
  private polygonPoints: { x: number; y: number }[] = []; // 保存多边形点
  private polygonPreviewLine: any = null; // 多边形预览线
  private polygonPreviewShape: any = null; // 多边形预览形状

  constructor(canvas: any) {
    this.canvas = canvas;
    this.fenceColor = {
      color: 'rgba(30, 144, 255, 0.3)',
      stroke: '#1e90ff',
      strokeWidth: 2
    };
  }

  /**
   * 获取围栏颜色配置
   */
  getFenceColor() {
    return this.fenceColor;
  }

  /**
   * 添加圆形围栏
   */
  addCircleFence(center: { x: number; y: number }, radius: number) {
    // 根据当前缩放比例调整边框宽度
    const currentZoom = this.canvas.getZoom();
    const adjustedStrokeWidth = this.fenceColor.strokeWidth / currentZoom;

    const circle = new Circle({
      left: center.x,
      top: center.y,
      radius: radius,
      fill: this.fenceColor.color,
      stroke: this.fenceColor.stroke,
      strokeWidth: adjustedStrokeWidth,
      originX: 'center',
      originY: 'center',
      lockUniScaling: true, // 锁定等比例缩放，确保圆形保持圆形
      strokeUniform: true, // 保持边框宽度一致，不随缩放变化
      noScaleCache: false,
      objectCaching: false
    });

    this.canvas.add(circle);
    return circle;
  }

  /**
   * 添加矩形围栏
   */
  addRectangleFence(position: { x: number; y: number }, width: number, height: number) {
    // 根据当前缩放比例调整边框宽度
    const currentZoom = this.canvas.getZoom();
    const adjustedStrokeWidth = this.fenceColor.strokeWidth / currentZoom;

    const rectangle = new Rect({
      left: position.x,
      top: position.y,
      width: width,
      height: height,
      fill: this.fenceColor.color,
      stroke: this.fenceColor.stroke,
      strokeWidth: adjustedStrokeWidth,
      strokeUniform: true, // 保持边框宽度一致，不随缩放变化
      noScaleCache: false,
      objectCaching: false
    });

    this.canvas.add(rectangle);
    return rectangle;
  }

  /**
   * 创建多边形围栏
   */
  createPolygonFence(points: { x: number; y: number }[]) {
    if (!points || points.length < 3) return null;

    // 根据当前缩放比例调整边框宽度
    const currentZoom = this.canvas.getZoom();
    const adjustedStrokeWidth = this.fenceColor.strokeWidth / currentZoom;

    const polygon = new Polygon(points, {
      fill: this.fenceColor.color,
      stroke: this.fenceColor.stroke,
      strokeWidth: adjustedStrokeWidth,
      strokeUniform: true // 保持边框宽度一致，不随缩放变化
    });

    this.canvas.add(polygon);
    return polygon;
  }
  
  /**
   * 添加多边形点
   * @param point 新的多边形点坐标
   */
  addPolygonPoint(point: { x: number; y: number }) {
    if (!this.polygonDrawing) {
      this.startPolygonDrawing();
    }
    
    this.polygonPoints.push(point);
    this.updatePolygonPreview();
    return true;
  }
  
  /**
   * 完成多边形绘制
   * @param lastPoint 最后一个点（可选）
   * @returns 创建的多边形对象，如果点数不足则返回null
   */
  finishPolygonDrawing(lastPoint?: { x: number; y: number }): any {
    // 如果提供了最后一个点，检查是否与第一个点接近
    if (lastPoint && this.polygonPoints.length > 0) {
      const firstPoint = this.polygonPoints[0];
      const distToFirst = Math.sqrt(
        Math.pow(lastPoint.x - firstPoint.x, 2) + 
        Math.pow(lastPoint.y - firstPoint.y, 2)
      );
      
      // 如果距离较远，添加最后一个点；否则自动闭合
      if (distToFirst > 10) {
        this.polygonPoints.push(lastPoint);
      }
    }
    
    // 确保至少有3个点
    if (this.polygonPoints.length < 3) {
      console.warn('多边形至少需要3个点');
      this.clearPolygonPreview();
      this.polygonDrawing = false;
      this.polygonPoints = [];
      return null;
    }
    
    // 创建最终的多边形
    const currentZoom = this.canvas.getZoom();
    const adjustedStrokeWidth = this.fenceColor.strokeWidth / currentZoom;

    const polygon = new Polygon(this.polygonPoints, {
      fill: this.fenceColor.color,
      stroke: this.fenceColor.stroke,
      strokeWidth: adjustedStrokeWidth,
      strokeUniform: true, // 保持边框宽度一致，不随缩放变化
      selectable: false,
      evented: false,
      objectCaching: false
    });
    (polygon as any).id = 'fence';
    
    this.canvas.add(polygon);
    
    // 清理预览
    this.clearPolygonPreview();
    
    // 重置状态
    const resultPoints = [...this.polygonPoints];
    this.polygonDrawing = false;
    this.polygonPoints = [];
    
    console.log('多边形绘制完成，点数:', resultPoints.length);
    this.canvas.renderAll();
    
    return polygon;
  }

  /**
   * 设置圆形围栏
   */
  setCircleFence(circle: any, point: { x: number; y: number }, startPoint: { x: number; y: number }) {
    if (!circle) return;
    
    // 计算半径
    const dx = point.x - startPoint.x;
    const dy = point.y - startPoint.y;
    const radius = Math.sqrt(dx * dx + dy * dy);
    
    // 根据当前缩放比例调整边框宽度，确保在所有缩放级别下都保持一致的视觉效果
    const currentZoom = this.canvas.getZoom();
    const adjustedStrokeWidth = this.fenceColor.strokeWidth / currentZoom;

    // 根据缩放比例调整最小半径，确保在高缩放下也能轻松创建
    const minRadius = 1 / currentZoom;

    // 更新圆形属性
    circle.set({
      radius: Math.max(radius, minRadius),
      lockUniScaling: true, // 锁定等比例缩放，确保圆形保持圆形
      strokeWidth: adjustedStrokeWidth,
      strokeUniform: true // 保持边框宽度一致，不随缩放变化
    });
    

    
    this.canvas.renderAll();
  }

  /**
   * 设置矩形围栏
   */
  setRectangleFence(rectangle: any, point: { x: number; y: number }, startPoint: { x: number; y: number }) {
    if (!rectangle) return;
    
    // 计算宽高
    const width = Math.abs(point.x - startPoint.x);
    const height = Math.abs(point.y - startPoint.y);
    
    // 确定矩形左上角位置
    const left = Math.min(point.x, startPoint.x);
    const top = Math.min(point.y, startPoint.y);
    
    // 根据当前缩放比例调整边框宽度，确保在所有缩放级别下都保持一致的视觉效果
    const currentZoom = this.canvas.getZoom();
    const adjustedStrokeWidth = this.fenceColor.strokeWidth / currentZoom;

    // 根据缩放比例调整最小尺寸，确保在高缩放下也能轻松创建
    const minSize = 1 / currentZoom;

    // 更新矩形属性
    rectangle.set({
      left: left,
      top: top,
      width: Math.max(width, minSize),
      height: Math.max(height, minSize),
      strokeWidth: adjustedStrokeWidth,
      strokeUniform: true // 保持边框宽度一致，不随缩放变化
    });
    

    
    this.canvas.renderAll();
  }

  /**
   * 更新半径线
   */
  updateRadiusLine(radiusLine: any, center: { x: number; y: number }, endPoint: { x: number; y: number }) {
    if (!radiusLine) return;
    
    radiusLine.set({
      x1: center.x,
      y1: center.y,
      x2: endPoint.x,
      y2: endPoint.y
    });
    
    this.canvas.renderAll();
  }







  /**
   * 开始多边形绘制
   */
  startPolygonDrawing() {
    this.polygonDrawing = true;
    this.polygonPoints = [];
    this.clearPolygonPreview();
    console.log('开始多边形绘制');
  }

  /**
   * 是否正在绘制多边形
   */
  isPolygonDrawing() {
    return this.polygonDrawing;
  }

  /**
   * 取消多边形绘制
   */
  cancelPolygonDrawing() {
    this.polygonDrawing = false;
    this.polygonPoints = [];
    this.clearPolygonPreview();
    console.log('取消多边形绘制');
  }

  /**
   * 更新多边形橡皮筋效果
   * @param mousePoint 当前鼠标位置
   */
  updatePolygonRubberBand(mousePoint: { x: number; y: number }) {
    if (!this.polygonDrawing || this.polygonPoints.length === 0) return;
    
    // 清除之前的预览
    this.clearPolygonPreview();

    const currentZoom = this.canvas.getZoom();
    const adjustedStrokeWidth = this.fenceColor.strokeWidth / currentZoom;
    
    // 如果只有一个点，绘制从第一个点到鼠标的线
    if (this.polygonPoints.length === 1) {
      this.polygonPreviewLine = new Line(
        [this.polygonPoints[0].x, this.polygonPoints[0].y, mousePoint.x, mousePoint.y],
        {
          stroke: this.fenceColor.stroke,
          strokeWidth: adjustedStrokeWidth,
          strokeUniform: true,
          selectable: false,
          evented: false,
          objectCaching: false
        }
      );
      (this.polygonPreviewLine as any).id = 'polygon-preview-line';
      this.canvas.add(this.polygonPreviewLine);
    }
    // 如果有两个或更多点，绘制完整的预览多边形
    else if (this.polygonPoints.length >= 2) {
      // 创建包含鼠标位置的临时点数组
      const previewPoints = [...this.polygonPoints, mousePoint];

      // 创建预览多边形（使用更透明的填充）
      const previewFill = this.fenceColor.color.replace(/[\d\.]+\)$/g, '0.1)'); // 将透明度改为0.1

      this.polygonPreviewShape = new Polygon(previewPoints, {
        fill: previewFill,
        stroke: this.fenceColor.stroke,
        strokeWidth: adjustedStrokeWidth,
        strokeUniform: true,
        selectable: false,
        evented: false,
        objectCaching: false
      });
      (this.polygonPreviewShape as any).id = 'polygon-preview';

      this.canvas.add(this.polygonPreviewShape);
    }
    
    this.canvas.renderAll();
  }
  
  /**
   * 更新多边形预览
   */
  private updatePolygonPreview() {
    // 清除之前的预览
    this.clearPolygonPreview();
    
    // 如果有足够的点，创建临时多边形
    if (this.polygonPoints.length >= 3) {
      const currentZoom = this.canvas.getZoom();
      const adjustedStrokeWidth = this.fenceColor.strokeWidth / currentZoom;

      this.polygonPreviewShape = new Polygon(this.polygonPoints, {
        fill: this.fenceColor.color,
        stroke: this.fenceColor.stroke,
        strokeWidth: adjustedStrokeWidth,
        strokeUniform: true,
        selectable: false,
        evented: false,
        objectCaching: false
      });
      (this.polygonPreviewShape as any).id = 'polygon-preview';

      this.canvas.add(this.polygonPreviewShape);
    }
    
    this.canvas.renderAll();
  }
  
  /**
   * 清除多边形预览
   */
  private clearPolygonPreview() {
    // 移除预览线
    if (this.polygonPreviewLine) {
      this.canvas.remove(this.polygonPreviewLine);
      this.polygonPreviewLine = null;
    }
    
    // 移除预览形状
    if (this.polygonPreviewShape) {
      this.canvas.remove(this.polygonPreviewShape);
      this.polygonPreviewShape = null;
    }
    
    // 清除所有预览相关的对象
    const objectsToRemove = this.canvas.getObjects().filter((obj: any) => 
      obj.id === 'polygon-preview' || 
      obj.id === 'polygon-preview-line'
    );
    
    objectsToRemove.forEach((obj: any) => {
      this.canvas.remove(obj);
    });
  }
}

/**
 * 统一的区域/围栏尺寸标注管理器
 * 整合了围栏和区域功能，避免重复
 */
export class AreaFenceManager {
  private fenceFactory: FenceFactory;

  constructor(canvas: any) {
    this.fenceFactory = new FenceFactory(canvas);
  }

  /**
   * 创建围栏
   */
  createFence(position: AreaPosition) {
    if (!position || position.type === 'none') return null;

    try {
      switch (position.type) {
        case 'circle':
          const circlePoints = position.points as CirclePoints;
          const circle = this.fenceFactory.addCircleFence(
            { x: circlePoints.x, y: circlePoints.y },
            circlePoints.radius
          );
          return circle;

        case 'rectangle':
          const rectPoints = position.points as RectanglePoints;
          const rectangle = this.fenceFactory.addRectangleFence(
            { x: rectPoints.x, y: rectPoints.y },
            rectPoints.width,
            rectPoints.height
          );
          return rectangle;

        case 'polygon':
          const polyPoints = position.points as PolygonPoints;
          if (polyPoints.length < 3) return null;
          
          const polygon = this.fenceFactory.createPolygonFence(polyPoints);
          return polygon;

        default:
          return null;
      }
    } catch (error) {
      console.error('创建围栏失败:', error);
      return null;
    }
  }





  /**
   * 获取围栏工厂实例
   */
  getFenceFactory() {
    return this.fenceFactory;
  }
} 


